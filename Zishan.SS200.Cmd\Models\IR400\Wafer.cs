using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.Process;
using Zishan.SS200.Cmd.Models.Process;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// Wafer类
    /// </summary>
    public class Wafer : BindableBase
    {
        /// <summary>
        /// CassetteID，及流水号ID号
        /// </summary>
        public string CassetteId { get => _CassetteId; set => SetProperty(ref _CassetteId, value); }
        private string _CassetteId;

        /// <summary>
        /// Wafer编号：1~25
        /// </summary>
        public int WaferNo { get => _WaferNo; private init => SetProperty(ref _WaferNo, value); }
        private readonly int _WaferNo;

        public string PrefixedWaferNo
        {
            get { return "槽_" + WaferNo; }
        }

        public string Name { get; set; }

        /// <summary>
        /// True在左边Cassette,否则在右边Cassette
        /// </summary>
        //public bool IsInLeft { get; set; }

        /// <summary>
        /// Wafer所在腔体WaferSide：左边、右边
        /// </summary>

        public EnuChamberWaferSide ChamberWaferSide { get; set; }

        /// <summary>
        /// 未知面，针对非机械臂选Unknown
        /// </summary>
        public EnuArmFetchSide ArmFetchSide { get; set; }

        /// <summary>
        /// Wafer状态
        /// </summary>
        public EnuWaferStatus WaferStatus
        {
            get { return _WaferStatus; }
            set
            {
                SetProperty(ref _WaferStatus, value);
                UpdateToolTip();
            }
        }
        private EnuWaferStatus _WaferStatus;

        /// <summary>
        /// WaferToolTip提示
        /// </summary>
        public string CurWaferToolTip { get => _CurWaferToolTip; set => SetProperty(ref _CurWaferToolTip, value); }
        private string _CurWaferToolTip;

        // /// <summary>
        // /// 扫片状态
        // /// </summary>
        // public EnuWaferMappingStatus MappingStatus { get => _MappingStatus; set => SetProperty(ref _MappingStatus, value); }
        // private EnuWaferMappingStatus _MappingStatus;

        /// <summary>
        /// 工艺处理结果状态，未完成、工艺故障、自动完成、手动完成
        /// </summary>
        public EnuProcessStatus ProcessStatus { get => _ProcessStatus; set => SetProperty(ref _ProcessStatus, value); }
        private EnuProcessStatus _ProcessStatus;

        // /// <summary>
        // /// 位置号:Slot号，在Cassette的Slot号
        // /// </summary>
        // public Pos WaferPos { get => _WaferPos; set => SetProperty(ref _WaferPos, value); }
        // private Pos _WaferPos;

        /// <summary>
        /// 整个工艺是否处理完成
        /// </summary>
        public bool IsFinisheded { get => _IsFinisheded; set => SetProperty(ref _IsFinisheded, value); }
        private bool _IsFinisheded;

        #region 工艺路线管理

        /// <summary>
        /// 当前工艺步骤
        /// </summary>
        public EnuProcessStep CurrentProcessStep { get => _CurrentProcessStep; set => SetProperty(ref _CurrentProcessStep, value); }
        private EnuProcessStep _CurrentProcessStep;

        /// <summary>
        /// 当前工艺路线阶段
        /// </summary>
        public EnuProcessRouteStage CurrentRouteStage { get => _CurrentRouteStage; set => SetProperty(ref _CurrentRouteStage, value); }
        private EnuProcessRouteStage _CurrentRouteStage;

        //这里添加下个工艺路线阶段EnuProcessRouteStage，用来后面去往哪个工艺做判断

        /// <summary>
        /// 下个工艺路线阶段，用于判断后续要去往哪个工艺
        /// </summary>
        public EnuProcessRouteStage NextRouteStage { get => _NextRouteStage; set => SetProperty(ref _NextRouteStage, value); }
        private EnuProcessRouteStage _NextRouteStage;

        /// <summary>
        /// 当前所在位置
        /// </summary>
        public EnuLocationStationType CurrentLocation { get => _CurrentLocation; set => SetProperty(ref _CurrentLocation, value); }
        private EnuLocationStationType _CurrentLocation;

        /// <summary>
        /// 下一个目标位置
        /// </summary>
        public EnuLocationStationType NextLocation { get => _NextLocation; set => SetProperty(ref _NextLocation, value); }
        private EnuLocationStationType _NextLocation;

        /// <summary>
        /// 工艺路线配置
        /// </summary>
        public ProcessRouteConfig RouteConfig { get; set; }

        /// <summary>
        /// 完整工艺路线（按顺序）- 从配置中动态获取
        /// </summary>
        public List<EnuLocationStationType> ProcessRoute => RouteConfig?.GetCompleteRoute() ?? new List<EnuLocationStationType>();

        /// <summary>
        /// 当前工艺步骤开始时间
        /// </summary>
        public DateTime? ProcessStepStartTime { get => _ProcessStepStartTime; set => SetProperty(ref _ProcessStepStartTime, value); }
        private DateTime? _ProcessStepStartTime;

        #endregion 工艺路线管理

        ///// <summary>
        ///// wafer整个机械臂抓取的历史记录
        ///// </summary>
        ////public List<ArmFetchHistory> ListArmFetchHistory { get; set; }

        public Wafer(EnuChamberWaferSide chamberWaferSide, int id)
        {
            ChamberWaferSide = chamberWaferSide;
            WaferNo = id;
            Name = $"Wafer_{id}";
            WaferStatus = EnuWaferStatus.Have;

            // 初始化工艺路线
            InitializeProcessRoute();
        }

        public override string ToString()
        {
            string strResult = $"Wafer_{WaferNo}{(ChamberWaferSide == EnuChamberWaferSide.LeftWafers ? "L" : "R")}_{(IsFinisheded ? "完成" : "未完")}";

            if (ArmFetchSide != EnuArmFetchSide.Unknow)
            {
                strResult += $"[{ArmFetchSide.ToString()}]";
            }
            return strResult;
        }

        /// <summary>
        /// 拖拽显示的字符串
        /// </summary>
        /// <returns></returns>
        public string ToShortString()
        {
            return $"Wafer_{WaferNo}_{(IsFinisheded ? "完成" : "未完")}";
        }

        #region 工艺路线管理方法

        /// <summary>
        /// 初始化标准工艺路线：Cassette → ChamberA → CoolingTop → Cassette
        /// </summary>
        public void InitializeProcessRoute()
        {
            // 创建默认工艺路线配置
            RouteConfig = new ProcessRouteConfig();

            // 初始化状态
            CurrentProcessStep = EnuProcessStep.WaitingInCassette;
            CurrentRouteStage = EnuProcessRouteStage.StartCassette;
            NextRouteStage = EnuProcessRouteStage.Chamber; // 下个阶段是Chamber
            CurrentLocation = RouteConfig.GetLocationForStage(EnuProcessRouteStage.StartCassette);
            NextLocation = RouteConfig.GetLocationForStage(EnuProcessRouteStage.Chamber);
            ProcessStepStartTime = DateTime.Now;
        }

        /// <summary>
        /// 设置自定义工艺路线
        /// </summary>
        /// <param name="chamberType">指定Chamber类型</param>
        /// <param name="coolingType">指定Cooling类型</param>
        public void SetCustomProcessRoute(EnuLocationStationType chamberType, EnuLocationStationType coolingType)
        {
            // 创建自定义工艺路线配置
            RouteConfig = new ProcessRouteConfig(chamberType, coolingType);

            // 更新下一个位置
            if (CurrentProcessStep == EnuProcessStep.WaitingInCassette)
            {
                NextLocation = RouteConfig.GetLocationForStage(EnuProcessRouteStage.Chamber);
            }
        }

        /// <summary>
        /// 动态设置Chamber类型
        /// </summary>
        /// <param name="chamberType">Chamber类型</param>
        public void SetChamberType(EnuLocationStationType chamberType)
        {
            RouteConfig?.SetChamberType(chamberType);

            // 如果当前正在等待去Chamber，更新下一位置
            if (CurrentProcessStep == EnuProcessStep.WaitingInCassette ||
                CurrentProcessStep == EnuProcessStep.TransferToChamber)
            {
                NextLocation = RouteConfig.GetLocationForStage(EnuProcessRouteStage.Chamber);
            }
        }

        /// <summary>
        /// 动态设置Cooling类型
        /// </summary>
        /// <param name="coolingType">Cooling类型</param>
        public void SetCoolingType(EnuLocationStationType coolingType)
        {
            RouteConfig?.SetCoolingType(coolingType);

            // 如果当前正在等待去Cooling，更新下一位置
            if (CurrentProcessStep == EnuProcessStep.ProcessingInChamber ||
                CurrentProcessStep == EnuProcessStep.TransferToCooling)
            {
                NextLocation = RouteConfig.GetLocationForStage(EnuProcessRouteStage.Cooling);
            }
        }

        /// <summary>
        /// 移动到下一个工艺步骤
        /// </summary>
        /// <returns>是否成功移动到下一步</returns>
        public bool MoveToNextStep()
        {
            var nextStep = GetNextStep();
            if (nextStep == null) return false;

            // 更新工艺步骤
            CurrentProcessStep = nextStep.Value;

            // 更新工艺路线阶段
            UpdateCurrentRouteStage();

            // 更新当前位置
            CurrentLocation = NextLocation;
            ProcessStepStartTime = DateTime.Now;

            // 更新下一个位置
            UpdateNextLocation();

            // 更新ToolTip
            UpdateToolTip();

            return true;
        }

        /// <summary>
        /// 更新当前工艺路线阶段和下个工艺路线阶段
        /// </summary>
        private void UpdateCurrentRouteStage()
        {
            CurrentRouteStage = CurrentProcessStep switch
            {
                EnuProcessStep.WaitingInCassette => EnuProcessRouteStage.StartCassette,
                EnuProcessStep.TransferToChamber => EnuProcessRouteStage.StartCassette,
                EnuProcessStep.ProcessingInChamber => EnuProcessRouteStage.Chamber,
                EnuProcessStep.TransferToCooling => EnuProcessRouteStage.Chamber,
                EnuProcessStep.CoolingInChamber => EnuProcessRouteStage.Cooling,
                EnuProcessStep.TransferBackToCassette => EnuProcessRouteStage.Cooling,
                EnuProcessStep.CompletedInCassette => EnuProcessRouteStage.EndCassette,
                _ => EnuProcessRouteStage.StartCassette
            };

            // 更新下个工艺路线阶段，用于判断后续要去往哪个工艺
            NextRouteStage = CurrentProcessStep switch
            {
                EnuProcessStep.WaitingInCassette => EnuProcessRouteStage.Chamber,
                EnuProcessStep.TransferToChamber => EnuProcessRouteStage.Chamber,
                EnuProcessStep.ProcessingInChamber => EnuProcessRouteStage.Cooling,
                EnuProcessStep.TransferToCooling => EnuProcessRouteStage.Cooling,
                EnuProcessStep.CoolingInChamber => EnuProcessRouteStage.EndCassette,
                EnuProcessStep.TransferBackToCassette => EnuProcessRouteStage.EndCassette,
                EnuProcessStep.CompletedInCassette => EnuProcessRouteStage.StartCassette, // 工艺完成，可以开始新的工艺
                _ => EnuProcessRouteStage.Chamber
            };
        }

        /// <summary>
        /// 获取下一个工艺步骤
        /// </summary>
        /// <returns>下一个工艺步骤，如果已完成则返回null</returns>
        public EnuProcessStep? GetNextStep()
        {
            return CurrentProcessStep switch
            {
                EnuProcessStep.WaitingInCassette => EnuProcessStep.TransferToChamber,
                EnuProcessStep.TransferToChamber => EnuProcessStep.ProcessingInChamber,
                EnuProcessStep.ProcessingInChamber => EnuProcessStep.TransferToCooling,
                EnuProcessStep.TransferToCooling => EnuProcessStep.CoolingInChamber,
                EnuProcessStep.CoolingInChamber => EnuProcessStep.TransferBackToCassette,
                EnuProcessStep.TransferBackToCassette => EnuProcessStep.CompletedInCassette,
                EnuProcessStep.CompletedInCassette => null, // 已完成
                _ => null
            };
        }

        /// <summary>
        /// 更新下一个位置
        /// </summary>
        private void UpdateNextLocation()
        {
            if (RouteConfig == null) return;

            NextLocation = CurrentProcessStep switch
            {
                EnuProcessStep.WaitingInCassette => RouteConfig.GetLocationForStage(EnuProcessRouteStage.Chamber),
                EnuProcessStep.TransferToChamber => RouteConfig.GetLocationForStage(EnuProcessRouteStage.Chamber),
                EnuProcessStep.ProcessingInChamber => RouteConfig.GetLocationForStage(EnuProcessRouteStage.Cooling),
                EnuProcessStep.TransferToCooling => RouteConfig.GetLocationForStage(EnuProcessRouteStage.Cooling),
                EnuProcessStep.CoolingInChamber => RouteConfig.GetLocationForStage(EnuProcessRouteStage.EndCassette),
                EnuProcessStep.TransferBackToCassette => RouteConfig.GetLocationForStage(EnuProcessRouteStage.EndCassette),
                EnuProcessStep.CompletedInCassette => EnuLocationStationType.None,
                _ => EnuLocationStationType.None
            };
        }

        /// <summary>
        /// 获取工艺进度百分比
        /// </summary>
        /// <returns>进度百分比 (0-100)</returns>
        public double GetProcessProgress()
        {
            return CurrentProcessStep switch
            {
                EnuProcessStep.WaitingInCassette => 0,
                EnuProcessStep.TransferToChamber => 16.67,
                EnuProcessStep.ProcessingInChamber => 33.33,
                EnuProcessStep.TransferToCooling => 50,
                EnuProcessStep.CoolingInChamber => 66.67,
                EnuProcessStep.TransferBackToCassette => 83.33,
                EnuProcessStep.CompletedInCassette => 100,
                _ => 0
            };
        }

        /// <summary>
        /// 判断工艺是否完成
        /// </summary>
        /// <returns>是否完成</returns>
        public bool IsProcessCompleted()
        {
            return CurrentProcessStep == EnuProcessStep.CompletedInCassette;
        }

        /// <summary>
        /// 获取当前工艺步骤描述
        /// </summary>
        /// <returns>步骤描述</returns>
        public string GetCurrentStepDescription()
        {
            return CurrentProcessStep switch
            {
                EnuProcessStep.WaitingInCassette => "在Cassette中等待",
                EnuProcessStep.TransferToChamber => "传输到Chamber",
                EnuProcessStep.ProcessingInChamber => "Chamber中处理",
                EnuProcessStep.TransferToCooling => "传输到Cooling",
                EnuProcessStep.CoolingInChamber => "Cooling中冷却",
                EnuProcessStep.TransferBackToCassette => "传输回Cassette",
                EnuProcessStep.CompletedInCassette => "工艺完成",
                _ => "未知状态"
            };
        }

        /// <summary>
        /// 获取下个工艺路线阶段描述
        /// </summary>
        /// <returns>下个阶段描述</returns>
        public string GetNextRouteStageDescription()
        {
            return NextRouteStage switch
            {
                EnuProcessRouteStage.StartCassette => "起始Cassette",
                EnuProcessRouteStage.Chamber => "Chamber工艺",
                EnuProcessRouteStage.Cooling => "Cooling冷却",
                EnuProcessRouteStage.EndCassette => "结束Cassette",
                _ => "未知阶段"
            };
        }

        /// <summary>
        /// 判断是否可以进入下个工艺阶段
        /// </summary>
        /// <returns>是否可以进入下个阶段</returns>
        public bool CanEnterNextRouteStage()
        {
            return NextRouteStage switch
            {
                EnuProcessRouteStage.Chamber => CurrentProcessStep == EnuProcessStep.WaitingInCassette ||
                                               CurrentProcessStep == EnuProcessStep.TransferToChamber,
                EnuProcessRouteStage.Cooling => CurrentProcessStep == EnuProcessStep.ProcessingInChamber ||
                                               CurrentProcessStep == EnuProcessStep.TransferToCooling,
                EnuProcessRouteStage.EndCassette => CurrentProcessStep == EnuProcessStep.CoolingInChamber ||
                                                   CurrentProcessStep == EnuProcessStep.TransferBackToCassette,
                EnuProcessRouteStage.StartCassette => CurrentProcessStep == EnuProcessStep.CompletedInCassette,
                _ => false
            };
        }

        /// <summary>
        /// 更新ToolTip信息，包含工艺路线信息
        /// </summary>
        private void UpdateToolTip()
        {
            try
            {
                var progress = GetProcessProgress();
                var stepDesc = GetCurrentStepDescription();
                var nextLocationDesc = NextLocation == EnuLocationStationType.None ? "无" : NextLocation.ToString();
                var routeDesc = RouteConfig?.GetRouteDescription() ?? "未配置";

                CurWaferToolTip = $"L{WaferNo}，当前状态：{WaferStatus}\n" +
                                 $"工艺步骤：{stepDesc}\n" +
                                 $"当前工艺阶段：{CurrentRouteStage}\n" +
                                 $"下个工艺阶段：{NextRouteStage}\n" +
                                 $"当前位置：{CurrentLocation}\n" +
                                 $"下一位置：{nextLocationDesc}\n" +
                                 $"工艺路线：{routeDesc}\n" +
                                 $"工艺进度：{progress:F1}%";
            }
            catch
            {
                // 如果在初始化过程中调用，使用简单的ToolTip
                CurWaferToolTip = $"L{WaferNo}，当前状态：{WaferStatus}";
            }
        }

        #endregion 工艺路线管理方法
    }
}